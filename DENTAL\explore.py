import os
import json
import cv2
import numpy as np
from torch.utils.data import Dataset, DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2
import torch.optim.lr_scheduler as lr_scheduler
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from tqdm import tqdm
import segmentation_models_pytorch as smp
import random
import matplotlib.pyplot as plt
import torch
from torchviz import make_dot
from torch.utils.tensorboard import SummaryWriter
from datetime import datetime
from functions import *

def train_with_config(epochs, arch, encoder_name, class_name, output_base=None):
    
    sample_output_folder = os.path.join(output_base, "sample_output", class_name)
    model_output_folder = os.path.join(output_base, "model_output", class_name)
    
    # 統一使用單一檔案路徑檢查已訓練模型
    summary_metrics_path = os.path.join(output_base, f"summary_metrics_{class_name}.csv")
    
    # 檢查摘要指標檔案是否存在
    if not os.path.exists(summary_metrics_path):
        with open(summary_metrics_path, "w") as f:
            f.write("model_name,epoch_loss,precision,recall,f1,iou,lr\n")
    
    # 檢查已訓練的架構
    arch_tried = []
    with open(summary_metrics_path, "r") as f:
        lines = f.readlines()
        for line in lines[1:]:
            arch_name = line.split(",")[0]
            arch_tried.append(arch_name)
            print(f"已訓練架構: {arch_name}")
    
    # 檢查當前架構是否已訓練
    current_arch = f"{arch}_{encoder_name}"
    if current_arch in arch_tried:
        print(f"{current_arch} 已訓練過，跳過...")
        return
        
    # 創建所需資料夾
    os.makedirs(sample_output_folder, exist_ok=True)
    if not os.path.exists(model_output_folder):
        os.makedirs(model_output_folder)
    if not os.path.exists(sample_output_folder):
        os.makedirs(sample_output_folder)
    
    # 創建或清空訓練樣本資料夾
    if not os.path.exists(sample_output_folder + "train/"):
        os.makedirs(sample_output_folder + "train/")
    else:
        # 清空資料夾
        for f in os.listdir(sample_output_folder + "train/"):
            os.remove(os.path.join(sample_output_folder + "train/", f))
    
    # 創建或清空測試樣本資料夾
    if not os.path.exists(sample_output_folder + "test/"):
        os.makedirs(sample_output_folder + "test/")
    else:
        # 清空資料夾
        for f in os.listdir(sample_output_folder + "test/"):
            os.remove(os.path.join(sample_output_folder + "test/", f))
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model_name = f"{arch}_{encoder_name}"
    model_output_folder = os.path.join(model_output_folder, model_name)
    os.makedirs(model_output_folder, exist_ok=True)

    # === 模型建立 ===
    model_class = getattr(smp, arch)
    model = model_class(encoder_name=encoder_name, encoder_weights="imagenet", classes=1, activation=None)
    model = nn.DataParallel(model).to(device)

    # === 資料載入 ===
    transform = A.Compose([
        A.Normalize(mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225)),
        ToTensorV2()
    ])
    train_dataset = DentalDataset(
        folder=output_base + "train/" + class_name,
        transform=transform,
        save_samples=True,
        save_dir=sample_output_folder + "train/",
        num_samples_to_save=10
    )

    test_dataset = DentalDataset(
        folder=output_base + "test/" + class_name,
        transform=transform,
        save_samples=True,
        save_dir=sample_output_folder + "test/",
        num_samples_to_save=10
    )
    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False, num_workers=4)

    # === 訓練配置 ===
    criterion = TverskyLoss(alpha=0.3, beta=0.7)
    optimizer = optim.Adam(model.parameters(), lr=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=10, verbose=True)

    # 初始化最佳指標和早停計數器
    best_iou = 0.0
    best_iou_model_weights = None
    best_loss_model_weights = None
    best_loss = float('inf')
    early_stop_patience = 20
    early_stop_counter = 0
    
    # 在訓練開始前創建 TensorBoard writer
    writer = SummaryWriter(model_output_folder)

    # 開始訓練循環
    for epoch in tqdm(range(epochs)):
        model.train()
        total_loss = 0.0
        for imgs, masks in train_loader:
            imgs, masks = imgs.to(device), masks.to(device)
            optimizer.zero_grad()
            outputs = model(imgs)
            loss = criterion(outputs, masks)
            loss.backward()
            optimizer.step()
            total_loss += loss.item() * imgs.size(0)
        epoch_loss = total_loss / len(train_loader.dataset)

        model.eval()
        with torch.no_grad():
            precision, recall, f1, iou = evaluate_model(model, test_loader, device)
        scheduler.step(epoch_loss)
        print(f"Epoch {epoch+1}/{epochs}, Loss: {epoch_loss:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}, IoU: {iou:.4f}, LR: {optimizer.param_groups[0]['lr']:.4e}")
        
        # 記錄訓練指標到 TensorBoard
        writer.add_scalar('Loss/train', epoch_loss, epoch)
        writer.add_scalar('Metrics/precision', precision, epoch)
        writer.add_scalar('Metrics/recall', recall, epoch)
        writer.add_scalar('Metrics/f1', f1, epoch)
        writer.add_scalar('Metrics/iou', iou, epoch)
        writer.add_scalar('LR', optimizer.param_groups[0]['lr'], epoch)
        
        # 早停機制和權重保存
        if iou > best_iou:
            best_iou = iou
            early_stop_counter = 0
            # 保存當前最佳權重
            best_iou_model_weights = model.state_dict().copy()
        else:
            early_stop_counter += 1
            print(f"Epoch {epoch+1}: No improvement, EarlyStop counter: {early_stop_counter}/{early_stop_patience}")
        
        if epoch_loss < best_loss:
            best_loss = epoch_loss
            early_stop_counter = 0
            # 保存當前最佳權重
            best_loss_model_weights = model.state_dict().copy()
            
        if early_stop_counter >= early_stop_patience:
            print(f"Early stopping triggered at epoch {epoch+1}.")
            break
    
    # 關閉 TensorBoard writer
    writer.close()
    
    # 將結果寫入摘要指標檔案
    with open(summary_metrics_path, "a") as f:
        f.write(f"{model_name},{best_loss:.4f},{precision:.4f},{recall:.4f},{f1:.4f},{best_iou:.4f},{optimizer.param_groups[0]['lr']:.4e}\n")

    # 保存最終模型
    torch.save(model.state_dict(), os.path.join(model_output_folder, f"{model_name}_final.pth"))
    # 保存最佳 IoU 模型
    torch.save(best_iou_model_weights, os.path.join(model_output_folder, f"{model_name}_best_iou.pth"))
    # 保存最佳 Loss 模型
    torch.save(best_loss_model_weights, os.path.join(model_output_folder, f"{model_name}_best_loss.pth"))
    # 保存模型架構
    dot = make_dot(model(imgs), params=dict(model.named_parameters()))
    dot.format = 'png'
    dot.attr(rankdir='LR')
    dot.render(os.path.join(model_output_folder, f"{model_name}_architecture"))
    # 保存模型摘要
    with open(os.path.join(model_output_folder, f"{model_name}_summary.txt"), "w") as f:
        f.write(str(model))
    # 保存模型圖
    writer = SummaryWriter(model_output_folder)
    writer.add_graph(model, imgs)
    writer.close()

selected_encoders = [
    "resnet34",
#   "resnext50_32x4d",
    "se_resnext50_32x4d",
#   "densenet121",
#   "efficientnet-b4",
#   "timm-tf_efficientnet_lite0",
#   "mobilenet_v2",
#   "timm-resnest50d",
    "mit_b2",
    "timm-regnety_040"
]
all_archs = ['Unet', "UnetPlusPlus", 'DeepLabV3', 'DeepLabV3Plus']

for arch in all_archs:
    for enc in selected_encoders:
        try:
            train_with_config(
                200,
                arch, 
                enc, 
                "I_P_mixed", 
                output_base="/home/<USER>/dental/dental/training_results/" + "2025-04-25-12-09" + "/"
                )
            print(f"完成 {arch} + {enc}")
        except Exception as e:
            print(f"跳過 {arch} + {enc}，因為錯誤：{e}")