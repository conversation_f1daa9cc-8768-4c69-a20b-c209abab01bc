import torch
import os
from torchvision import transforms
from PIL import Image
from torchvision.transforms import ToTensor, Normalize
import segmentation_models_pytorch as smp
import torch.nn as nn
import visualize_label
import random
import os
# weights are here : /home/<USER>/dental/dental_predict/DeepLabV3_resnet34/DeepLabV3_resnet34_final.pth



# load the model
arch = "DeepLabV3"
encoder_name = "resnet34"
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model_name = "DeepLabV3_resnet34"



model_class = getattr(smp, arch)
model = model_class(encoder_name=encoder_name, encoder_weights=None, classes=1, activation=None)

# check cuda availability
if torch.cuda.is_available():
    model = model.cuda()

model = nn.DataParallel(model).to(device)


# load weights
model_path = '/home/<USER>/dental/dental/training_results/2025-04-25-12-09/model_output/I_P_mixed/DeepLabV3_resnet34/DeepLabV3_resnet34_final.pth'
state_dict = torch.load(model_path, map_location=device)
model.load_state_dict(state_dict)

# set to evaluation mode
model.eval()




# def visualize_annotations(image_path, json_path, output_path=None):

# random select a json file and visualize it
target_dir_combined = "combined_data"


json_files_I = [os.path.join(target_dir_combined, 'I', o) for o in os.listdir(os.path.join(target_dir_combined, 'I')) if o.endswith('.json')]
random_json = random.choice(json_files_I)
image_path_I = random_json.replace('.json', '.jpg')
visualize_label.visualize_annotations(image_path_I, random_json, output_path='images/annotated_images/'+ image_path_I.split('.')[0].split('/')[-1]+ 'I.jpg')
json_files_P = [os.path.join(target_dir_combined, 'P', o) for o in os.listdir(os.path.join(target_dir_combined, 'P')) if o.endswith('.json')]
random_json = random.choice(json_files_P)
image_path_P = random_json.replace('.json', '.jpg')
visualize_label.visualize_annotations(image_path_P, random_json, output_path='images/annotated_images/'+ image_path_P.split('.')[0].split('/')[-1]+ 'P.jpg')


# preprocess the input file (here we will just normalize the image)
# load the image
image = Image.open(image_path_I)
# resize the image
image = image.resize((512, 512))
# convert the image to tensor
image = ToTensor()(image)
# normalize the image
normalize = Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
image = normalize(image)
# add a batch dimension
image = image.unsqueeze(0)
    
# make the prediction
with torch.no_grad():
    output = model(image)
    pred_mask = torch.sigmoid(output)
    pred_mask = pred_mask.squeeze().cpu().numpy()
    binary_mask = (pred_mask > 0.5).astype('uint8')  # Convert to binary mask
    # Save the binary mask
    output_image = Image.fromarray(binary_mask * 255)  # Scale to 0-255
    output_image.save('/home/<USER>/dental/dental/output/binary_mask.png')
print("pred_mask min:", pred_mask.min(), "max:", pred_mask.max())
from PIL import Image, ImageOps

# 讀取原始圖片（確保是 RGB）
orig_image = Image.open(image_path_I).resize((512, 512)).convert("RGB")

# 產生彩色遮罩（紅色，半透明）
mask_img = Image.fromarray((binary_mask * 255).astype('uint8'))
mask_img = mask_img.convert("L")  # 轉成單通道灰階
mask_color = (255, 0, 0, 50)  # 紅色，透明度100/255
color_mask = Image.new("RGBA", mask_img.size, mask_color)
# 只在 mask 為 255 的地方疊加顏色
mask_img = Image.composite(color_mask, Image.new("RGBA", mask_img.size), mask_img)
overlay = Image.alpha_composite(orig_image.convert("RGBA"), mask_img)

# show the overlay
overlay.show()

# 儲存結果
overlay.save('/home/<USER>/dental/dental/output/overlay_result.png')



# preprocess the input file (here we will just normalize the image)
# load the image
image = Image.open(image_path_P)
# resize the image
image = image.resize((512, 512))
# convert the image to tensor
image = ToTensor()(image)
# normalize the image
normalize = Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
image = normalize(image)
# add a batch dimension
image = image.unsqueeze(0)
    
# make the prediction
with torch.no_grad():
    output = model(image)
    pred_mask = torch.sigmoid(output)
    pred_mask = pred_mask.squeeze().cpu().numpy()
    binary_mask = (pred_mask > 0.5).astype('uint8')  # Convert to binary mask
    # Save the binary mask
    output_image = Image.fromarray(binary_mask * 255)  # Scale to 0-255
    output_image.save('/home/<USER>/dental/dental/output/binary_mask.png')
print("pred_mask min:", pred_mask.min(), "max:", pred_mask.max())
from PIL import Image, ImageOps

# 讀取原始圖片（確保是 RGB）
orig_image = Image.open(image_path_P).resize((512, 512)).convert("RGB")

# 產生彩色遮罩（紅色，半透明）
mask_img = Image.fromarray((binary_mask * 255).astype('uint8'))
mask_img = mask_img.convert("L")  # 轉成單通道灰階
mask_color = (255, 0, 0, 50)  # 紅色，透明度100/255
color_mask = Image.new("RGBA", mask_img.size, mask_color)
# 只在 mask 為 255 的地方疊加顏色
mask_img = Image.composite(color_mask, Image.new("RGBA", mask_img.size), mask_img)
overlay = Image.alpha_composite(orig_image.convert("RGBA"), mask_img)

# show the overlay
overlay.show()

# 儲存結果
overlay.save('/home/<USER>/dental/dental/output/overlay_result.png')