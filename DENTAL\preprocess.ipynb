import os
import shutil
import tqdm
import cv2

source_dir = 'org_data'
target_dir_split = "split_data"
target_dir_combined = "combined_data"

# 獲取源目錄中第一層子目錄的列表
sub_dirs = [os.path.join(source_dir, o) for o in os.listdir(source_dir) if os.path.isdir(os.path.join(source_dir,o))]
sub_dirs.sort()

# 創建目標目錄中的'I'和'P'文件夾
os.makedirs(os.path.join(target_dir_combined,"I"), exist_ok=True)
os.makedirs(os.path.join(target_dir_combined,"P"), exist_ok=True)

# 初始化計數器
json_count_I = 0
json_count_P = 0
jpg_count_I = 0
jpg_count_P = 0

# 遍歷每個子目錄
for sub in tqdm.tqdm(sub_dirs):
    id = sub.split('/')[1]
    # 創建以id命名的文件夾
    os.makedirs(os.path.join(target_dir_split, id), exist_ok=True)
    # 在該文件夾中創建'I'和'P'子文件夾
    os.makedirs(os.path.join(target_dir_split, id, 'I'), exist_ok=True)
    os.makedirs(os.path.join(target_dir_split, id, 'P'), exist_ok=True)
    
    # 遍歷子目錄中的所有文件
    for root, dirs, files in os.walk(sub):
        for file in files:
            if file.endswith('.jpg'):
                if 'I' in root:
                    jpg_count_I += 1
                    # 複製jpg文件到目標目錄
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_split, id, 'I', file))
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_combined, 'I', file))
                elif 'P' in root:
                    jpg_count_P += 1
                    # 複製jpg文件到目標目錄
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_split, id, 'P', file))
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_combined, 'P', file))
                else:
                    print('error with file: ', os.path.join(root, file))
            if file.endswith('.json'):
                if 'I' in root:
                    json_count_I += 1
                    # 複製json文件到目標目錄
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_split, id, 'I', file))
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_combined, 'I', file))
                elif 'P' in root:
                    json_count_P += 1
                    # 複製json文件到目標目錄
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_split, id, 'P', file))
                    shutil.copy(os.path.join(root, file), os.path.join(target_dir_combined, 'P', file))

# 打印總計數
print(f'Total number of I images: {jpg_count_I}')
print(f'Total number of P images: {jpg_count_P}')
print(f'Total number of I json files: {json_count_I}')
print(f'Total number of P json files: {json_count_P}')

import visualize_label

# def visualize_annotations(image_path, json_path, output_path=None):

# random select a json file and visualize it

import random

json_files_I = [os.path.join(target_dir_combined, 'I', o) for o in os.listdir(os.path.join(target_dir_combined, 'I')) if o.endswith('.json')]
random_json = random.choice(json_files_I)
image_path = random_json.replace('.json', '.jpg')
visualize_label.visualize_annotations(image_path, random_json, output_path='images/annotated_images/'+ image_path.split('.')[0].split('/')[-1]+ 'I.jpg')

json_files_P = [os.path.join(target_dir_combined, 'P', o) for o in os.listdir(os.path.join(target_dir_combined, 'P')) if o.endswith('.json')]
random_json = random.choice(json_files_P)
image_path = random_json.replace('.json', '.jpg')
visualize_label.visualize_annotations(image_path, random_json, output_path='images/annotated_images/'+ image_path.split('.')[0].split('/')[-1]+ 'P.jpg')

import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
import random
import json

# Load the dental image and its annotations
json_files_I = [os.path.join(target_dir_combined, 'I', o) for o in os.listdir(os.path.join(target_dir_combined, 'I')) if o.endswith('.json')]
random_json = random.choice(json_files_I)
image_path = random_json.replace('.json', '.jpg')

original_image = cv2.imread(image_path)
original_image_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)

# Load annotations from JSON
with open(random_json, 'r') as file:
    annotations = json.load(file)

# Draw annotations on the original image
annotated_image = original_image_rgb.copy()
for shape in annotations.get("shapes", []):
    if shape["shape_type"] == "polygon":
        points = np.array(shape["points"], dtype=np.int32)
        cv2.polylines(annotated_image, [points], isClosed=True, color=(255, 0, 0), thickness=2)

# 1. Histogram Equalization (Grayscale)
gray_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)
hist_eq = cv2.equalizeHist(gray_image)

# 2. CLAHE
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
clahe_image = clahe.apply(gray_image)

# 3. Gaussian Blur
gaussian_blur = cv2.GaussianBlur(original_image, (5, 5), 0)

# 4. Bilateral Filtering
bilateral_filter = cv2.bilateralFilter(original_image, 9, 75, 75)

# 5. Laplacian Edge Enhancement
laplacian = cv2.Laplacian(gray_image, cv2.CV_64F)
laplacian_normalized = cv2.normalize(laplacian, None, 0, 255, cv2.NORM_MINMAX)
laplacian_enhanced = cv2.convertScaleAbs(laplacian_normalized)

# 6. Unsharp Masking
gaussian_blurred = cv2.GaussianBlur(original_image, (9, 9), 10.0)
unsharp_image = cv2.addWeighted(original_image, 1.5, gaussian_blurred, -0.5, 0)

# 7. White Balance Adjustment
def apply_white_balance(img):
    result = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    l, a, b = cv2.split(result)
    l = cv2.equalizeHist(l)
    balanced = cv2.merge((l, a, b))
    return cv2.cvtColor(balanced, cv2.COLOR_LAB2BGR)

white_balanced_image = apply_white_balance(original_image)

# Plotting comparison of all methods
titles = [
    "Original with Annotations", "Histogram Equalization",
    "CLAHE", "Gaussian Blur",
    "Bilateral Filtering", "Laplacian",
    "Unsharp Masking", "White Balance"
]
images = [
    annotated_image, cv2.cvtColor(hist_eq, cv2.COLOR_GRAY2RGB),
    cv2.cvtColor(clahe_image, cv2.COLOR_GRAY2RGB), cv2.cvtColor(gaussian_blur, cv2.COLOR_BGR2RGB),
    cv2.cvtColor(bilateral_filter, cv2.COLOR_BGR2RGB), cv2.cvtColor(laplacian_enhanced, cv2.COLOR_GRAY2RGB),
    cv2.cvtColor(unsharp_image, cv2.COLOR_BGR2RGB), cv2.cvtColor(white_balanced_image, cv2.COLOR_BGR2RGB)
]

plt.figure(figsize=(16, 12))
for i, (title, img) in enumerate(zip(titles, images)):
    plt.subplot(2, 4, i + 1)
    plt.imshow(img)
    plt.title(title)
    plt.axis("off")
plt.tight_layout()
plt.savefig('images/image_enhancement/image_enhancement_compared_with_annotations'+ image_path.split('.')[0].split('/')[-1]+'.png')
plt.show()


import cv2
import numpy as np
from skimage.metrics import structural_similarity as ssim
import matplotlib.pyplot as plt
import json
import pandas as pd

# Load the dental image and its annotations
json_files_I = [os.path.join(target_dir_combined, 'I', o) for o in os.listdir(os.path.join(target_dir_combined, 'I')) if o.endswith('.json')]
random_json = random.choice(json_files_I)
image_path = random_json.replace('.json', '.jpg')

original_image = cv2.imread(image_path)
original_image_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
original_image_gray = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)

# Load annotations from JSON
with open(random_json, 'r') as file:
    annotations = json.load(file)

# Extract polygon points for the marked region
polygon = np.array(annotations['shapes'][0]['points'], dtype=np.int32)

# Create a mask for the annotated area
mask = np.zeros_like(original_image_gray, dtype=np.uint8)
cv2.fillPoly(mask, [polygon], 255)

# Apply filters to the original image
filters = {
    "Original": original_image_gray,
    "Histogram Equalization": cv2.equalizeHist(original_image_gray),
    "CLAHE": cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8)).apply(original_image_gray),
    "Gaussian Blur": cv2.GaussianBlur(original_image_gray, (5, 5), 0),
    "Bilateral Filtering": cv2.bilateralFilter(original_image_gray, 9, 75, 75),
    "Laplacian": cv2.convertScaleAbs(cv2.normalize(cv2.Laplacian(original_image_gray, cv2.CV_64F), None, 0, 255, cv2.NORM_MINMAX)),
    "Unsharp Masking": cv2.addWeighted(
        original_image_gray, 1.5, cv2.GaussianBlur(original_image_gray, (9, 9), 10.0), -0.5, 0
    ),
    "White Balance": cv2.cvtColor(apply_white_balance(original_image), cv2.COLOR_BGR2GRAY)
}

# Analyze the region inside the mask
results = {}
for name, filtered_image in filters.items():
    # Apply the mask to the filtered image
    masked_region = cv2.bitwise_and(filtered_image, filtered_image, mask=mask)

    # Calculate metrics
    mean_intensity = np.mean(masked_region[mask > 0])  # Mean pixel intensity in the region
    contrast = np.std(masked_region[mask > 0])  # Standard deviation as a measure of contrast
    edge_count = cv2.Canny(masked_region, 100, 200).sum()  # Count edge pixels

    # Store results
    results[name] = {
        "Mean Intensity": mean_intensity,
        "Contrast": contrast,
        "Edge Count": edge_count,
    }

# Display the results in a table
results_table = []
for filter_name, metrics in results.items():
    results_table.append({
        "Filter": filter_name,
        **metrics
    })

results_df = pd.DataFrame(results_table)
print(results_df)


# 所以我們比較了上述影像中的不同區域，並使用不同的影像增強技術來分析這些區域。我們發現，CLAHE和直方圖均衡化在這種情況下表現最好，因為它們提供了最高的對比度和邊緣檢測數量。
# 這些結果表明，這些技術可以幫助醫生更好地檢測和診斷牙齒問題。

221 + 162

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import cv2
# 指定資料夾
data_dir = "combined_data/P"

# 蒐集所有 json 檔案
json_files = [f for f in os.listdir(data_dir) if f.endswith(".json")]

# 儲存每張 mask 的面積（以 pixel 數量表示）
mask_areas = []

for json_file in json_files:
    json_path = os.path.join(data_dir, json_file)
    with open(json_path, "r", encoding="utf-8") as f:
        data = json.load(f)
    
    height, width = data.get("imageHeight"), data.get("imageWidth")
    mask = np.zeros((height, width), dtype=np.uint8)

    for shape in data.get("shapes", []):
        pts = np.array(shape["points"], dtype=np.int32)
        cv2.fillPoly(mask, [pts], 1)

    area = np.sum(mask)
    mask_areas.append(area)

# 計算統計
average_area = np.mean(mask_areas)
min_area = np.min(mask_areas)
max_area = np.max(mask_areas)

import pandas as pd

# 可視化
plt.figure(figsize=(10, 4))
sns.histplot(mask_areas, bins=50, kde=True)
plt.title("Distribution of Mask Areas")
plt.xlabel("Mask Area (pixels)")
plt.ylabel("Frequency")
plt.grid(True)
plt.tight_layout()

# Create a DataFrame to display the statistics
stats_df = pd.DataFrame({
    "Metric": ["Average Area", "Minimum Area", "Maximum Area", "Number of Annotated Images"],
    "Value": [average_area, min_area, max_area, len(mask_areas)]
})

# Display the DataFrame
print(stats_df)

# show the maximum area image
max_area_index = mask_areas.index(max_area)
max_area_json = json_files[max_area_index]
max_area_image_path = os.path.join(data_dir, max_area_json.replace('.json', '.jpg'))
max_area_image = cv2.imread(max_area_image_path)
max_area_image_rgb = cv2.cvtColor(max_area_image, cv2.COLOR_BGR2RGB)
max_area_json_path = os.path.join(data_dir, max_area_json)
# Load annotations from JSON
with open(max_area_json_path, 'r') as file:
    annotations = json.load(file)
# Extract polygon points for the marked region
polygon = np.array(annotations['shapes'][0]['points'], dtype=np.int32)
# Create a mask for the annotated area
mask = np.zeros_like(max_area_image_rgb, dtype=np.uint8)
cv2.fillPoly(mask, [polygon], (255, 255, 255))
# Apply the mask to the image
masked_image = cv2.bitwise_and(max_area_image_rgb, mask)
# Plot the image with the mask
plt.figure(figsize=(10, 10))
plt.imshow(masked_image)
plt.axis('off')
plt.title(f"Image with Maximum Mask Area: {max_area} pixels")
plt.tight_layout()
plt.savefig('images/maximum_mask_area_image.png')
plt.show()
# show the minimum area image
min_area_index = mask_areas.index(min_area)
min_area_json = json_files[min_area_index]
min_area_image_path = os.path.join(data_dir, min_area_json.replace('.json', '.jpg'))
min_area_image = cv2.imread(min_area_image_path)
min_area_image_rgb = cv2.cvtColor(min_area_image, cv2.COLOR_BGR2RGB)
min_area_json_path = os.path.join(data_dir, min_area_json)
# Load annotations from JSON
with open(min_area_json_path, 'r') as file:
    annotations = json.load(file)
# Extract polygon points for the marked region
polygon = np.array(annotations['shapes'][0]['points'], dtype=np.int32)
# Create a mask for the annotated area
mask = np.zeros_like(min_area_image_rgb, dtype=np.uint8)
cv2.fillPoly(mask, [polygon], (255, 255, 255))
# Apply the mask to the image
masked_image = cv2.bitwise_and(min_area_image_rgb, mask)
# Plot the image with the mask
plt.figure(figsize=(10, 10))
plt.imshow(masked_image)
plt.axis('off')
plt.title(f"Image with Minimum Mask Area: {min_area} pixels")
plt.tight_layout()
plt.savefig('images/minimum_mask_area_image.png')
plt.show()

import os
import json
import cv2
import albumentations as A
import datetime
import shutil
from tqdm import tqdm
import numpy as np
import random

# 參數設定
min_mask_area = 200
annotated_to_unannotated_ratio = 2
train_ratio = 0.8

current_date = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M")
base_result_path = f"/home/<USER>/dental/dental/training_results/{current_date}"

os.makedirs(base_result_path, exist_ok=True)
whole_dataset_path = os.path.join(base_result_path, "whole_dataset")
os.makedirs(whole_dataset_path, exist_ok=True)
augmentation_description_file = os.path.join(base_result_path, "augmentation_parameters.txt")
summary_file = os.path.join(base_result_path, "summary_statistics.txt")

# 計算所有 shape 中 polygons 的總面積
def compute_mask_area(shapes):
    area = 0.0
    for shape in shapes:
        pts = np.array(shape["points"], dtype=np.float32)
        if len(pts) >= 3:
            area += cv2.contourArea(pts)
    return area

all_samples = {"I": [], "P": []}
stats = {"I": {"annotated": 0, "unannotated": 0}, "P": {"annotated": 0, "unannotated": 0}}
final_counts = {"train": {"I": 0, "P": 0}, "test": {"I": 0, "P": 0}}

### 改我!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
# 增強管道（供寫入紀錄） 
augmentation_pipeline = A.Compose([
    A.CLAHE(clip_limit=1.5, tile_grid_size=(5, 5), p=1.0),
    A.Rotate(limit=15, p=1.0),
    A.Affine(
        translate_percent=0.0,
        scale=(0.7, 1.3),
        rotate=0,
        fit_output=True,
        p=1.0
    ),
    A.Resize(512, 512)
], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))
### 改我!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!


# 寫入 augmentation pipeline 描述
with open(augmentation_description_file, "w") as f:
    f.write("使用比率：\n")
    f.write(f"訓練集與測試集比例：{train_ratio}\n")
    f.write(f"有標註與無標註比例：{annotated_to_unannotated_ratio}\n")
    f.write("使用的增強方法如下：\n")
    f.write(str(augmentation_pipeline))

# 處理資料
def process_dataset(class_name):
    print(f"\n開始處理 {class_name} 類別資料...")

    for subset in ["train", "test"]:
        input_folder = f"/home/<USER>/dental/dental/Aug_data/{subset}/{class_name}"
        output_folder = os.path.join(whole_dataset_path, class_name)
        os.makedirs(output_folder, exist_ok=True)

        transform = augmentation_pipeline
        filter_transform = A.Compose([A.Resize(512, 512)])

        num_augmentations = 10 if subset == "train" else 3
        all_image_files = [f for f in os.listdir(input_folder) if f.lower().endswith((".jpg", ".png", ".jpeg"))]

        for filename in tqdm(all_image_files, desc=f"{subset}_{class_name}", ncols=100):
            image_path = os.path.join(input_folder, filename)
            base_name = os.path.splitext(filename)[0]
            json_path = os.path.join(input_folder, base_name + ".json")

            image = cv2.imread(image_path)
            if image is None:
                continue

            if os.path.exists(json_path):
                with open(json_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                mask_area = compute_mask_area(data["shapes"])
                if mask_area < min_mask_area:
                    continue

                # Resize 原始有標註影像後儲存
                resized_image = filter_transform(image=image)["image"]
                output_image_path = os.path.join(output_folder, filename)
                cv2.imwrite(output_image_path, resized_image)

                shutil.copy(json_path, os.path.join(output_folder, os.path.basename(json_path)))
                all_samples[class_name].append((output_image_path, True))
                stats[class_name]["annotated"] += 1

                all_points = []
                points_per_shape = []
                for shape in data["shapes"]:
                    pts = shape["points"]
                    points_per_shape.append(len(pts))
                    all_points.extend(pts)

                for i in range(num_augmentations):
                    transformed = transform(image=image, keypoints=all_points)
                    aug_image = transformed["image"]
                    aug_keypoints = transformed["keypoints"]

                    new_data = data.copy()
                    new_shapes = []
                    index = 0
                    for shape, num_points in zip(data["shapes"], points_per_shape):
                        new_shape = shape.copy()
                        new_shape["points"] = aug_keypoints[index:index+num_points]
                        index += num_points
                        new_shapes.append(new_shape)
                    new_data["shapes"] = new_shapes

                    aug_img_name = f"{base_name}_aug_{i}.jpg"
                    aug_json_name = f"{base_name}_aug_{i}.json"
                    cv2.imwrite(os.path.join(output_folder, aug_img_name), aug_image)
                    with open(os.path.join(output_folder, aug_json_name), "w", encoding="utf-8") as f:
                        json.dump(new_data, f, indent=4)

                    all_samples[class_name].append((os.path.join(output_folder, aug_img_name), True))
                    stats[class_name]["annotated"] += 1

            else:
                transformed = filter_transform(image=image)
                filtered_image = transformed["image"]
                output_image_path = os.path.join(output_folder, filename)
                cv2.imwrite(output_image_path, filtered_image)

                empty_annotation = {
                    "version": "4.5.6",
                    "flags": {},
                    "shapes": [],
                    "imagePath": filename,
                    "imageData": None,
                    "imageHeight": 512,
                    "imageWidth": 512
                }
                output_json_path = os.path.join(output_folder, base_name + ".json")
                with open(output_json_path, "w", encoding="utf-8") as f:
                    json.dump(empty_annotation, f, indent=4)

                all_samples[class_name].append((output_image_path, False))
                stats[class_name]["unannotated"] += 1

def split_dataset():
    for class_name in ["I", "P"]:
        annotated = [f for f in all_samples[class_name] if f[1]]
        unannotated = [f for f in all_samples[class_name] if not f[1]]

        random.shuffle(annotated)
        random.shuffle(unannotated)

        total_annotated = len(annotated)
        max_unannotated = total_annotated * annotated_to_unannotated_ratio
        selected_unannotated = unannotated[:int(max_unannotated)]
        
        print(f"Class {class_name} - 採用 {len(annotated)} 張有標註，{len(selected_unannotated)} 張無標註，共 {len(annotated) + len(selected_unannotated)} 張")

        total = annotated + selected_unannotated
        random.shuffle(total)

        train_count = int(len(total) * train_ratio)
        train_set = total[:train_count]
        test_set = total[train_count:]

        final_counts["train"][class_name] = len(train_set)
        final_counts["test"][class_name] = len(test_set)

        for dataset_name, dataset in zip(["train", "test"], [train_set, test_set]):
            target_folder = os.path.join(base_result_path, dataset_name, class_name)
            mixed_folder = os.path.join(base_result_path, dataset_name, "I_P_mixed")
            os.makedirs(target_folder, exist_ok=True)
            os.makedirs(mixed_folder, exist_ok=True)

            for img_path, _ in dataset:
                filename = os.path.basename(img_path)
                json_path = os.path.splitext(img_path)[0] + ".json"

                # 複製到該類別資料夾
                shutil.copy(img_path, os.path.join(target_folder, filename))
                if os.path.exists(json_path):
                    shutil.copy(json_path, os.path.join(target_folder, os.path.basename(json_path)))

                # 同時也複製到混合資料夾
                shutil.copy(img_path, os.path.join(mixed_folder, filename))
                if os.path.exists(json_path):
                    shutil.copy(json_path, os.path.join(mixed_folder, os.path.basename(json_path)))
                    with open(json_path, "r", encoding="utf-8") as f_json:
                            json_data = json.load(f_json)
                            if json_data.get("shapes"):  # 判斷有無標註
                                mixed_stats["annotated"] += 1
                            else:
                                mixed_stats["unannotated"] += 1
                else:
                    mixed_stats["unannotated"] += 1  # 萬一漏掉 json 仍記為無標註

                # 記錄訓練/測試數量
                if dataset_name == "train":
                    mixed_stats["train_count"] += 1
                else:
                    mixed_stats["test_count"] += 1
            


mixed_stats = {
    "annotated": 0,
    "unannotated": 0,
    "train_count": 0,
    "test_count": 0
}
    

# 主流程執行
print(f"開始資料處理，所有結果將暫存於：{whole_dataset_path}")
process_dataset("I")
process_dataset("P")
split_dataset()



# 顯示統計並寫入文字檔
print("\n===== 資料統計結果 =====")
with open(summary_file, "w") as f:
    f.write("===== 資料統計結果 =====\n")
    for class_name in ["I", "P"]:
        msg = (
            f"類別 {class_name}:\n"
            f"  有標記圖片數量（含增強）: {stats[class_name]['annotated']}\n"
            f"  無標記圖片數量: {stats[class_name]['unannotated']}\n"
            f"  Whole Dataset 總數量: {len(all_samples[class_name])}\n"
            f"  訓練集數量: {final_counts['train'][class_name]}\n"
            f"  測試集數量: {final_counts['test'][class_name]}\n"
        )
        print(msg)
        f.write(msg + "\n")
    f.write("\n混合類別 I_P_mixed 統計：\n")
    msg = (
        f"  有標記圖片數量（含增強）: {mixed_stats['annotated']}\n"
        f"  無標記圖片數量: {mixed_stats['unannotated']}\n"
        f"  Whole Dataset 總數量: {mixed_stats['annotated'] + mixed_stats['unannotated']}\n"
        f"  訓練集數量: {mixed_stats['train_count']}\n"
        f"  測試集數量: {mixed_stats['test_count']}\n"
    )
    print("混合類別 I_P_mixed 統計：\n" + msg)
    f.write(msg)

print(f"\n詳細結果與統計已儲存至：{base_result_path}")
